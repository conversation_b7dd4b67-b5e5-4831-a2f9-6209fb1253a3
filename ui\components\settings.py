"""
设置页面组件

管理原料数据库和系统设置
"""

import ttkbootstrap as ttk
import pandas as pd
import shutil
import os
from typing import Optional, List, Dict
from ui.utils.styles import StyleManager
from ui.utils.dialogs import DialogUtils
from utils.constants import TABLE_COLUMNS, COLUMN_WIDTHS
from utils.file_handler import FileHandler


class SettingsTab:
    """设置选项卡组件"""

    def __init__(self, parent, calculator):
        self.parent = parent
        self.calculator = calculator
        self.frame = ttk.Frame(parent)
        self.materials_tree = None
        self.create_widgets()
        
        # 设置数据变更监听器
        self.calculator.materials_manager.add_change_listener(self._on_materials_data_changed)

    def _on_materials_data_changed(self, change_type: str, data=None):
        """处理材料数据变更事件"""
        try:
            if change_type in ['materials_loaded', 'selection_changed', 'data_updated',
                             'material_added', 'material_deleted', 'data_reset', 'data_cleared']:
                # 刷新材料表格
                if hasattr(self, 'materials_tree') and self.materials_tree:
                    self.refresh_materials_table()

            print(f"材料数据变更通知: {change_type}")

        except Exception as e:
            print(f"处理材料数据变更事件时出错: {e}")

    def create_widgets(self):
        """创建设置页面组件"""
        # 配置网格权重
        self.frame.grid_rowconfigure(0, weight=1)
        self.frame.grid_columnconfigure(0, weight=1)

        # 主容器
        main_frame = ttk.Frame(self.frame)
        main_frame.grid(row=0, column=0, sticky="nsew", padx=0, pady=30)
        main_frame.grid_rowconfigure(0, weight=1)
        main_frame.grid_columnconfigure(0, weight=1)

        # 原料表格区域
        self.create_materials_display(main_frame)

    def create_materials_display(self, parent):
        """创建原料显示区域"""
        # 确保样式已配置
        StyleManager.setup_modern_labelframe_style()
        StyleManager.setup_button_styles()
        StyleManager.setup_table_styles()

        display_frame = ttk.LabelFrame(
            parent,
            text="基础数据",
            style='Modern.TLabelframe',
            padding=25
        )
        display_frame.grid(row=0, column=0, sticky="nsew")
        display_frame.grid_rowconfigure(0, weight=1)
        display_frame.grid_columnconfigure(0, weight=1)

        # 表格容器
        table_container = ttk.Frame(display_frame)
        table_container.grid(row=0, column=0, sticky="nsew")
        table_container.grid_rowconfigure(0, weight=1)
        table_container.grid_columnconfigure(0, weight=1)

        # 使用统一的列配置
        columns = TABLE_COLUMNS['materials']

        # 创建Treeview
        self.materials_tree = ttk.Treeview(
            table_container,
            columns=columns,
            show="headings",
            height=25 
        )

        # 设置列标题和宽度
        for col in columns:
            self.materials_tree.heading(col, text=col)
            # 使用统一的列宽配置
            width = COLUMN_WIDTHS.get(col, 140)
            minwidth = width
            self.materials_tree.column(col, width=width, minwidth=minwidth, anchor="center", stretch=True)

        # 添加滚动条
        v_scrollbar = ttk.Scrollbar(table_container, orient="vertical", command=self.materials_tree.yview)
        h_scrollbar = ttk.Scrollbar(table_container, orient="horizontal", command=self.materials_tree.xview)
        self.materials_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # 填充数据
        self.refresh_materials_table()

        # 绑定双击事件
        self.materials_tree.bind('<Double-1>', self.on_materials_tree_double_click)

        # 布局
        self.materials_tree.grid(row=0, column=0, sticky="nsew")
        v_scrollbar.grid(row=0, column=1, sticky="ns")
        h_scrollbar.grid(row=1, column=0, sticky="ew")

        # 操作按钮区域
        self.create_button_section(display_frame)

    def create_button_section(self, parent):
        """创建按钮区域"""
        button_frame = ttk.Frame(parent)
        button_frame.grid(row=2, column=0, sticky="ew", pady=(15, 0))

        # 按钮容器 - 使用grid布局实现均匀分布
        buttons_container = ttk.Frame(button_frame)
        buttons_container.pack(fill="x", expand=True)

        # 配置列权重，实现均匀分布
        for i in range(6):
            buttons_container.grid_columnconfigure(i, weight=1)

        # 创建六个按钮，使用多任务页面的outline样式，均匀分布，增加按钮高度
        ttk.Button(
            buttons_container,
            text="初始化",
            command=self.initialize_materials,
            bootstyle="warning-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=0, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="模板",
            command=self.download_template,
            bootstyle="info-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=1, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="导入",
            command=self.batch_import_materials,
            bootstyle="primary-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=2, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="下载",
            command=self.download_current_data,
            bootstyle="success-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=3, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="添加行",
            command=self.manual_add_material,
            bootstyle="dark-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=4, sticky="ew", padx=2)

        ttk.Button(
            buttons_container,
            text="删除行",
            command=self.delete_selected_rows,
            bootstyle="danger-outline",
            width=10,
            padding=(10, 15)
        ).grid(row=0, column=5, sticky="ew", padx=2)

    def populate_materials_table(self):
        """填充材料表格数据"""
        # 清空现有数据
        for item in self.materials_tree.get_children():
            self.materials_tree.delete(item)

        # 获取数据
        materials_data = self.calculator.materials_manager.get_materials_data()

        # 如果没有数据，直接返回
        if not materials_data:
            return

        # 获取表格列的顺序（排除选择列）
        table_columns = self.materials_tree['columns'][1:]  # 跳过第一列（选择列）

        # 添加所有行数据
        for row_data in materials_data:
            values = []

            # 第一列：选择状态 - 使用更明确的复选框符号
            selected = row_data.get('_selected', False)
            values.append('☑' if selected else '☐')  # 使用标准复选框符号

            # 中间列：原始数据，按照表格列的顺序
            for col in table_columns:
                value = row_data.get(col, '')
                # 格式化显示值
                if value == '' or pd.isna(value):
                    values.append('')
                else:
                    values.append(str(value))

            # 插入行，设置标签用于样式
            item_id = self.materials_tree.insert("", "end", values=values)
            row_index = len(self.materials_tree.get_children()) - 1

            # 设置行样式：隔行变色
            if row_index % 2 == 0:
                self.materials_tree.item(item_id, tags=('evenrow',))
            else:
                self.materials_tree.item(item_id, tags=('oddrow',))

        # 设置样式
        StyleManager.configure_treeview_colors(self.materials_tree)

    def on_materials_tree_double_click(self, event, item=None, column=None):
        """处理材料表格双击事件"""
        if item is None:
            item = self.materials_tree.selection()[0] if self.materials_tree.selection() else None
        if not item:
            return

        # 获取点击的列
        if column is None and event is not None:
            column = self.materials_tree.identify_column(event.x)
        elif column is None:
            column = '#2'  # 默认编辑第二列
        elif isinstance(column, str) and not column.startswith('#'):
            # 如果传入的是列名，需要找到对应的列索引
            columns = self.materials_tree['columns']
            try:
                col_index = columns.index(column) + 1  # +1 因为列索引从1开始
                column = f'#{col_index}'
            except ValueError:
                column = '#2'  # 如果找不到列名，默认使用第二列

        column_index = int(column.replace('#', '')) - 1

        if column_index == 0:  # Pick列
            self.toggle_material_selection(item)
        else:  # 其他列 - 编辑
            self.edit_material_cell(item, column_index)

    def edit_material_cell(self, item, column_index):
        """编辑材料表格单元格"""
        # 获取行索引
        row_index = self.materials_tree.index(item)
        materials_data = self.calculator.materials_manager.get_materials_data()

        if row_index >= len(materials_data):
            return

        # 获取列名（跳过选择列）
        columns = self.materials_tree['columns']
        if column_index <= 0:
            return  # 不允许编辑选择列

        column_name = columns[column_index]
        current_value = str(materials_data[row_index].get(column_name, ''))

        # 获取单元格位置
        bbox = self.materials_tree.bbox(item, column_name)
        if not bbox:
            return

        # 创建编辑框
        edit_var = ttk.StringVar(value=current_value)
        edit_entry = ttk.Entry(
            self.materials_tree,
            textvariable=edit_var,
            font=('Microsoft YaHei', 11)
        )

        # 定位编辑框
        edit_entry.place(x=bbox[0], y=bbox[1], width=bbox[2], height=bbox[3])
        edit_entry.focus()
        edit_entry.select_range(0, 'end')

        def save_edit():
            new_value = edit_var.get().strip()
            # 使用材料管理器更新数据
            self.calculator.materials_manager.update_material_data(row_index, column_name, new_value)
            edit_entry.destroy()

        def cancel_edit():
            edit_entry.destroy()

        # 绑定事件
        edit_entry.bind('<Return>', lambda _: save_edit())
        edit_entry.bind('<Escape>', lambda _: cancel_edit())
        edit_entry.bind('<FocusOut>', lambda _: save_edit())

    def toggle_material_selection(self, item):
        """切换材料选择状态"""
        # 获取行索引
        row_index = self.materials_tree.index(item)

        # 使用材料管理器切换选择状态
        success = self.calculator.materials_manager.toggle_material_selection(row_index)

        if success:
            # 刷新表格显示（通过数据变更监听器自动处理）
            pass

    def refresh_materials_table(self):
        """刷新材料表格"""
        if hasattr(self, 'materials_tree') and self.materials_tree:
            self.populate_materials_table()

    def initialize_materials(self):
        """初始化原料数据"""
        try:
            # 确认操作
            if not DialogUtils.ask_yes_no(
                "确认初始化",
                "这将清空当前所有数据并从默认Excel文件重新加载。\n确定要继续吗？"
            ):
                return

            # 从默认Excel文件初始化
            success = self.calculator.materials_manager.load_materials_from_file('oxide_powders_initial.xlsx')

            if success:
                DialogUtils.show_info("初始化成功", "原料数据已成功初始化")
            else:
                DialogUtils.show_error("初始化失败", "无法从默认文件初始化数据，请检查文件是否存在")

        except Exception as e:
            DialogUtils.show_error("初始化失败", f"初始化时出错: {str(e)}")

    def download_template(self):
        """下载原料数据模板"""
        try:
            file_path = DialogUtils.get_save_file_path("oxide_powders_template", ".xlsx")
            if not file_path:
                return

            # 使用 FileHandler 获取正确的模板文件路径
            template_file = FileHandler.get_template_file_path()

            # 检查模板文件是否存在
            if not os.path.exists(template_file):
                DialogUtils.show_error("提示", f"未找到模板文件:\n{template_file}")
                return

            # 复制模板文件
            shutil.copy2(template_file, file_path)
            DialogUtils.show_info("提示", f"模板已保存到:\n{file_path}")

        except Exception as e:
            DialogUtils.show_error("提示", f"下载模板时出错:\n{str(e)}")

    def batch_import_materials(self):
        """批量导入原料数据"""
        try:
            file_path = DialogUtils.get_open_file_path("选择原料数据文件")
            if not file_path:
                return

            # 确认导入
            if not DialogUtils.ask_yes_no(
                "确认导入",
                "这将向当前数据库添加新的原料数据。\n确定要继续吗？"
            ):
                return

            # 导入数据
            success = self.calculator.materials_manager.import_from_excel(file_path)

            if success:
                DialogUtils.show_info("导入成功", "原料数据已成功导入")
            else:
                DialogUtils.show_error("导入失败", "导入数据时出错，请检查文件格式")

        except Exception as e:
            DialogUtils.show_error("导入失败", f"导入时出错: {str(e)}")

    def download_current_data(self):
        """下载当前数据"""
        try:
            file_path = DialogUtils.get_save_file_path("当前原料数据", ".xlsx")
            if not file_path:
                return

            # 获取当前数据
            materials_data = self.calculator.materials_manager.get_materials_data()

            if not materials_data:
                DialogUtils.show_error("导出失败", "没有数据可导出")
                return

            # 转换为DataFrame
            df = pd.DataFrame(materials_data)

            # 移除内部字段
            if '_selected' in df.columns:
                df = df.drop('_selected', axis=1)
            if 'id' in df.columns:
                df = df.drop('id', axis=1)

            # 导出到Excel
            df.to_excel(file_path, index=False)
            DialogUtils.show_info("✅ 导出成功", f"当前数据已保存到:\n{file_path}")

        except Exception as e:
            DialogUtils.show_error("❌ 导出失败", f"导出时出错:\n{str(e)}")

    def delete_selected_rows(self):
        """删除选中的行"""
        selected_items = self.materials_tree.selection()
        if not selected_items:
            DialogUtils.show_warning("未选择行", "请先选择要删除的行")
            return

        if not DialogUtils.ask_yes_no("确认删除", f"确定要删除选中的 {len(selected_items)} 行数据吗？"):
            return

        try:
            # 获取行索引（从后往前删除以避免索引变化）
            row_indices = []
            for item in selected_items:
                row_index = self.materials_tree.index(item)
                row_indices.append(row_index)

            # 按索引倒序排列
            row_indices.sort(reverse=True)

            # 删除数据
            deleted_count = 0
            for row_index in row_indices:
                if self.calculator.materials_manager.delete_material(row_index):
                    deleted_count += 1

            if deleted_count > 0:
                DialogUtils.show_info("删除成功", f"已成功删除 {deleted_count} 行数据")
            else:
                DialogUtils.show_error("删除失败", "删除数据时出错")

        except Exception as e:
            DialogUtils.show_error("删除失败", f"删除时出错: {str(e)}")

    def manual_add_material(self):
        """手动添加材料"""
        try:
            # 创建空白材料数据
            empty_material_data = {
                'Z': '',
                'Element': '',
                'Formula': '',
                'Ratio': '',
                'Excess': '',
                'Mr': '',
                'CAS': '',
                'Co.': '',
                'Product No.': '',
                'Purity': '',
                'Pricing': ''
            }

            # 直接添加空白行
            success = self.calculator.materials_manager.add_material(empty_material_data)
            if success:
                # 自动选中新添加的行进行编辑
                children = self.materials_tree.get_children()
                if children:
                    last_item = children[-1]
                    self.materials_tree.selection_set(last_item)
                    self.materials_tree.focus(last_item)
                    # 触发编辑模式，编辑元素列
                    self.on_materials_tree_double_click(None, item=last_item, column='Element')
            else:
                DialogUtils.show_error("添加失败", "添加材料时出错")

        except Exception as e:
            DialogUtils.show_error("添加失败", f"添加时出错: {str(e)}")

    def get_frame(self):
        """获取组件框架"""
        return self.frame
